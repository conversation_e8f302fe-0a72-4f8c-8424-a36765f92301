// Test file để kiểm tra tính năng giới hạn số lượng item cho từng tab

import 'package:flutter_test/flutter_test.dart';
import 'package:gp_core/core.dart';

void main() {
  group('TabSelectionLimit Tests', () {
    test('should return correct limit for each tab', () {
      final options = SelectInviteesOptions(
        tabSelectionLimits: {
          'member': 5,
          'department': 3,
          'role': 2,
          'group': 8,
          'bot': 1,
          'workspace': 1,
        },
      );

      // Test member tab limit
      expect(SelectInviteeTabs.member.getMaxSelectionLimit(options), 5);
      
      // Test department tab limit
      expect(SelectInviteeTabs.department.getMaxSelectionLimit(options), 3);
      
      // Test role tab limit
      expect(SelectInviteeTabs.role.getMaxSelectionLimit(options), 2);
      
      // Test group tab limit
      expect(SelectInviteeTabs.group.getMaxSelectionLimit(options), 8);
      
      // Test bot tab limit
      expect(SelectInviteeTabs.bot.getMaxSelectionLimit(options), 1);
      
      // Test workspace tab limit
      expect(SelectInviteeTabs.workspace.getMaxSelectionLimit(options), 1);
    });

    test('should return null when no limits are set', () {
      final options = SelectInviteesOptions();

      expect(SelectInviteeTabs.member.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.department.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.role.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.group.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.bot.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.workspace.getMaxSelectionLimit(options), null);
    });

    test('should return null for tabs not specified in limits', () {
      final options = SelectInviteesOptions(
        tabSelectionLimits: {
          'member': 5,
          'department': 3,
          // role, group, bot, workspace không được chỉ định
        },
      );

      expect(SelectInviteeTabs.member.getMaxSelectionLimit(options), 5);
      expect(SelectInviteeTabs.department.getMaxSelectionLimit(options), 3);
      expect(SelectInviteeTabs.role.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.group.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.bot.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.workspace.getMaxSelectionLimit(options), null);
    });

    test('should handle empty tabSelectionLimits map', () {
      final options = SelectInviteesOptions(
        tabSelectionLimits: {},
      );

      expect(SelectInviteeTabs.member.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.department.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.role.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.group.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.bot.getMaxSelectionLimit(options), null);
      expect(SelectInviteeTabs.workspace.getMaxSelectionLimit(options), null);
    });

    test('should serialize and deserialize tabSelectionLimits correctly', () {
      final originalOptions = SelectInviteesOptions(
        title: "Test",
        tabSelectionLimits: {
          'member': 10,
          'department': 5,
          'role': 3,
        },
      );

      // Convert to JSON
      final json = originalOptions.toJson();
      
      // Convert back from JSON
      final deserializedOptions = SelectInviteesOptions.fromJson(json);

      expect(deserializedOptions.tabSelectionLimits?['member'], 10);
      expect(deserializedOptions.tabSelectionLimits?['department'], 5);
      expect(deserializedOptions.tabSelectionLimits?['role'], 3);
      expect(deserializedOptions.tabSelectionLimits?['group'], null);
    });
  });
}

/*
Để chạy test này:
1. Đảm bảo rằng file này được đặt trong thư mục test/
2. Chạy lệnh: flutter test test_tab_selection_limits.dart

Test cases bao gồm:
1. Kiểm tra việc lấy giới hạn đúng cho từng tab
2. Kiểm tra trường hợp không có giới hạn nào được thiết lập
3. Kiểm tra trường hợp chỉ một số tab có giới hạn
4. Kiểm tra trường hợp map giới hạn rỗng
5. Kiểm tra việc serialize/deserialize JSON
*/

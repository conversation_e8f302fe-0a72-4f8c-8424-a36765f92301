// V<PERSON> dụ về cách sử dụng tính năng giới hạn số lượng item có thể chọn cho từng tab

import 'package:gp_core/core.dart';

void exampleUsage() {
  // Ví dụ 1: <PERSON>i<PERSON><PERSON> hạn chọn tối đa 5 member, 3 department, 2 role
  final options1 = SelectInviteesOptions(
    title: "Chọn người tham gia",
    tabs: [
      SelectInviteeTabs.member,
      SelectInviteeTabs.department,
      SelectInviteeTabs.role,
    ],
    tabSelectionLimits: {
      'member': 5,        // Tối đa 5 member
      'department': 3,    // Tối đa 3 department
      'role': 2,          // Tối đa 2 role
    },
  );

  // Ví dụ 2: Chỉ giới hạn một số tab, các tab khác không giới hạn
  final options2 = SelectInviteesOptions(
    title: "Chọn người tham gia sự kiện",
    tabs: [
      SelectInviteeTabs.member,
      SelectInviteeTabs.group,
      SelectInviteeTabs.bot,
    ],
    tabSelectionLimits: {
      'member': 10,       // Tối đa 10 member
      'bot': 1,           // Tối đa 1 bot
      // group không có giới hạn
    },
  );

  // Ví dụ 3: Giới hạn tất cả các tab
  final options3 = SelectInviteesOptions(
    title: "Chọn thành viên dự án",
    tabs: [
      SelectInviteeTabs.member,
      SelectInviteeTabs.department,
      SelectInviteeTabs.role,
      SelectInviteeTabs.group,
      SelectInviteeTabs.bot,
      SelectInviteeTabs.workspace,
    ],
    tabSelectionLimits: {
      'member': 20,
      'department': 5,
      'role': 3,
      'group': 8,
      'bot': 2,
      'workspace': 1,
    },
  );

  // Ví dụ 4: Không có giới hạn (null hoặc không truyền tabSelectionLimits)
  final options4 = SelectInviteesOptions(
    title: "Chọn không giới hạn",
    tabs: [
      SelectInviteeTabs.member,
      SelectInviteeTabs.department,
    ],
    // tabSelectionLimits: null, // Không giới hạn
  );

  // Sử dụng
  // Get.toNamed(RouterName.selectInvitees, arguments: options1);
}

/*
Cách hoạt động:

1. Khi user chọn một item trong tab:
   - Hệ thống sẽ kiểm tra xem số lượng đã chọn có đạt giới hạn chưa
   - Nếu chưa đạt giới hạn: cho phép chọn bình thường
   - Nếu đã đạt giới hạn: hiển thị bottomsheet cảnh báo và không cho phép chọn thêm

2. Bottomsheet cảnh báo sẽ hiển thị:
   - Title: "Đã đạt giới hạn chọn [Tên Tab]"
   - Content: "Bạn chỉ có thể chọn tối đa [số lượng] [tên tab]"
   - Button: "Hiểu rồi" để đóng bottomsheet

3. Giới hạn áp dụng riêng biệt cho từng tab:
   - Tab Member: giới hạn số lượng member có thể chọn
   - Tab Department: giới hạn số lượng department có thể chọn
   - Tab Role: giới hạn số lượng role có thể chọn
   - Tab Group: giới hạn số lượng group có thể chọn
   - Tab Bot: giới hạn số lượng bot có thể chọn
   - Tab Workspace: giới hạn số lượng workspace có thể chọn

4. Cấu hình:
   - Nếu không truyền tabSelectionLimits hoặc truyền null: không có giới hạn
   - Nếu truyền tabSelectionLimits nhưng không có key cho tab nào đó: tab đó không có giới hạn
   - Chỉ những tab có key trong tabSelectionLimits mới bị giới hạn

5. Tương thích ngược:
   - Code cũ không truyền tabSelectionLimits vẫn hoạt động bình thường (không giới hạn)
   - Giới hạn limitUsers cũ vẫn hoạt động song song với giới hạn tab mới
*/

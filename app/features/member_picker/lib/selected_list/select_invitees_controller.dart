import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/bot/bot_response.dart';
import 'package:gp_core/models/gp_workspace.dart';
import 'package:gp_core/navigator/platform_navigator.dart';
import 'package:gp_feat_member_picker/bot/bot_controller.dart';
import 'package:gp_feat_member_picker/member_picker.dart';
import 'package:gp_feat_member_picker/picker_mixin.dart';
import 'package:gp_feat_member_picker/selected_list/invitees_selected_list_controller.dart';
import 'package:gp_feat_member_picker/selected_list/invitees_selected_list_page.dart';
import 'package:gp_feat_member_picker/utils/deeplink.dart';

import '../workspace/workspace_controller.dart';

typedef OnDataPicked = void Function();

class SelectInviteesController extends BaseListController
    with _TabMixin, GetSingleTickerProviderStateMixin, ArgumentConfigMixin
    implements ArgumentInitialMixin {
  final searchTextEditingController = TextEditingController();

  late InviteesListController inviteMembersListController;
  late OrganizationDepartmentSelectListController departmentController;
  late OrganizationRoleSelectListController roleController;
  late GroupSelectListController groupController;
  late BotController botController;
  late WorkspaceController workspaceController;

  final RxnString title = RxnString();
  final RxnString actionButtonTitle = RxnString();

  List<int> selectedMemberIds = [];
  bool fromNative = false;
  bool onBackMethodChannel = false;
  bool onDoneMethodChannel = false;

  List<OrganizationDepartment> _tempDeptsFlat = [];

  bool get isShowBtnViewSelectedMembers => svo.isShowBtnViewSelectedMembers;
  RxBool canPressDone = false.obs;
  bool isEditMode = false;
  bool removeAllSelectedOnTabChange = false;
  String onDoneMethodName = "";
  String groupId = "";

  bool? defaultCanPressDone;
  bool? needToCheckPressDone;

  SelectInviteesController({GPConnection? gpConnection})
      : super(gpConnection ?? GPConnectionConcrete());

  void initController() {
    inviteMembersListController = InviteesListController([]);
    departmentController = OrganizationDepartmentSelectListController();
    roleController = OrganizationRoleSelectListController();
    groupController = GroupSelectListController();
    botController = BotController();
    workspaceController = WorkspaceController(onDataPicked: () {
      onAppBarActionPressed();
    });
  }

  @override
  void onInit() {
    super.onInit();

    dynamic args = Get.arguments;

    try {
      if (args is Map<String, dynamic>) {
        args = SelectInviteesOptions.fromJson(args);
      }
    } catch (e, s) {
      logDebug('SelectInviteesController parse argument error: $e,\n $s');
    }

    if (args is SelectInviteesOptions) {
      if (args.workspaceId != null) {
        Constants.updateCurrentWorkspaceId(args.workspaceId ?? '');
      }

      // init sau khi update workspace_id
      initController();

      tabs.value = args.tabs;

      defaultCanPressDone = args.defaultCanPressDone;

      needToCheckPressDone = args.needToCheckPressDone;

      fromNative = args.fromNative ?? false;
      onBackMethodChannel = args.onBackMethodChannel;
      onDoneMethodChannel = args.onDoneMethodChannel;
      groupId = args.groupId;
      onDoneMethodName = args.onDoneMethodName;
      removeAllSelectedOnTabChange = args.removeAllSelectedOnTabChange;
      if (args.onlyCurrentWorkspace == true ||
          (fromNative && args.searchOnlyCurrentWorkspace == true)) {
        inviteMembersListController.setOnlyCurrentWorkspace();
      }

      initArguments(args);

      /*
        để tạm pickMode ở đây, sau bốc vào 1 controller chung,
        hiện mỗi controller con lại extends 1 cái, nên hơi khó, sau improve 1 lượt luôn
      */
      // TODO(seniorDev): improve code
      if (args.selectInviteesPickMode.isPickOne) {
        inviteMembersListController
            .setOnPickModeListener(onAppBarActionPressed);
        departmentController.setOnPickModeListener(onAppBarActionPressed);
        roleController.setOnPickModeListener(onAppBarActionPressed);
        groupController.setOnPickModeListener(onAppBarActionPressed);
        botController.setOnPickModeListener(onAppBarActionPressed);
      }

      switch (args.mode) {
        case SelectInviteesOptionsMode.selectNew:
          // không fill gì cả
          if (fromNative) {
            inviteMembersListController.filterOutMemberIds =
                args.filterOutMemberIds;
          }
          break;
        case SelectInviteesOptionsMode.selectNewWithValue:
          if (fromNative) {
            _fillSelectedObjectWithListIds(args);
          } else {
            _fillSelectedObjects(args);
            checkHasAnySelectedItem();
          }
          break;
        case SelectInviteesOptionsMode.edit:
          isEditMode = true;
          // lưu lại selectedMemberIds để hiện nút Xem x người đã tham gia
          selectedMemberIds = args.selectedMemberIds ?? [];
          // thành viên được thêm bởi admin = notRemovabale
          final notRemovableMember =
              args.notRemovableUserIds?.isNotEmpty == true
                  ? args.notRemovableUserIds
                  : args.membersAddedByAdminIds ?? [];
          notRemovableMember?.addAll(args.notRemovableMemberIds ?? []);
          if (notRemovableMember?.isNotEmpty == true) {
            for (var val in notRemovableMember!) {
              selectedMemberIds.addIf(!selectedMemberIds.contains(val), val);
            }
            final ignoreRemoveUserIds = args.notRemovableUserIds ?? [];
            inviteMembersListController.selectedMemberIds.value =
                ignoreRemoveUserIds.toSet();
          }

          if (fromNative) {
            inviteMembersListController.filterOutMemberIds =
                args.filterOutMemberIds;
            _fillSelectedObjectWithListIds(args);
          } else {
            _fillSelectedObjects(args);
            // reset
            inviteMembersListController.selectedItems.value = [];
            _fillSelectedObjectWithListIds(args);
          }
          break;
        case SelectInviteesOptionsMode.addOnly:
          // chỉ fill member ids, bỏ qua group, dept, role
          final argSelectedMemberIds = args.selectedMemberIds ?? [];
          inviteMembersListController.selectedMemberIds.value =
              argSelectedMemberIds.toSet();
          inviteMembersListController.filterOutMemberIds =
              args.filterOutMemberIds;

          // lưu lại selectedMemberIds để hiện nút Xem x người đã tham gia
          selectedMemberIds = argSelectedMemberIds;
          break;
        case SelectInviteesOptionsMode.viewOnly:
          if (args.selectedMemberIds != null) {
            final List<int> setNotRemovableMemberIds =
                args.selectedMemberIds!.toSet().toList();

            inviteMembersListController.selectedMemberIds.value
                .addAll(setNotRemovableMemberIds);

            _fillSelectedObjectWithListIds(args);
          }
          break;
      }

      title.value = args.title;
      actionButtonTitle.value = args.actionButtonTitle;
    } else {
      initController();
      tabs.value = [SelectInviteeTabs.member];
    }

    tabController = TabController(length: tabs.length, vsync: this);
    tabController.addListener(() {
      if (tabController.previousIndex != tabController.index &&
          removeAllSelectedOnTabChange) {
        clearSelected();
      }
      Utils.dismissKeyboard();

      hideTooltip();
    });
    // searchTextEditingController.addListener(() {
    //   final text = searchTextEditingController.text;
    //   _searchText.value = text;
    //   departmentController.searchTextValueChanged(text);
    //   roleController.searchTextValueChanged(text);
    //   groupController.searchTextValueChanged(text);
    // });

    // listen to update invitees list
    // debounce(_searchText, inviteMembersListController.searchTextValueChanged,
    //     time: 300.milliseconds);
  }

  void clearSelected() {
    inviteMembersListController.removeAllSelected();
    departmentController.removeAllSeleted();
    roleController.removeAllSelected();
    groupController.removeAllSelected();
    botController.removeAllSelected();
    workspaceController.removeAllSelected();
    checkHasAnySelectedItem();
  }

  void checkHasAnySelectedItem() {
    final List<Assignee> assignees =
        List.from(inviteMembersListController.selectedItems.value);
    final List<OrganizationDepartment> departments =
        List.from(departmentController.selectedDepartment.value);
    final List<OrganizationRole> roles =
        List.from(roleController.selectedRoles.value);
    final List<Conversation> threads =
        List.from(groupController.selectedItems.value);
    final List<ChatBotModel> bots =
        List.from(botController.selectedItemsNoDuplicate);
    final List<GPWorkspace> workspaces =
        List.from(workspaceController.selectedItemsNoDuplicate);

    final bool isNotEmpty = assignees.isNotEmpty ||
        departments.isNotEmpty ||
        roles.isNotEmpty ||
        threads.isNotEmpty ||
        bots.isNotEmpty ||
        workspaces.isNotEmpty;

    if (needToCheckPressDone == false) {
      canPressDone.value = true;
      return;
    }

    if (defaultCanPressDone == false) {
      canPressDone.value = isNotEmpty;
    } else {
      canPressDone.value = (isNotEmpty || isEditMode);
    }
  }

  void onBackPress() {
    if (fromNative && onBackMethodChannel) {
      MemberPickerDeeplink.onBackPress();
    }

    inviteMembersListController.svo?.onBackPressed?.call();

    Utils.back();
  }

  Future<void> onAppBarActionPressed() async {
    final List<Assignee> assignees;
    final List<String>? selectedMemberIds;
    if (svo.maxMemberFullResponseReturnNumber != null) {
      /// nếu native pass `max_member_full_response_return_number`,
      /// trả về n phần tử trong `inviteMembersListController.value`
      assignees = List.from(inviteMembersListController.value
          .take(svo.maxMemberFullResponseReturnNumber!));

      // trả về `selectedMemberIds`
      selectedMemberIds = inviteMembersListController.returnSelectedMemberIds;
    } else {
      if (fromNative) {
        assignees = List.from(inviteMembersListController.value);
      } else {
        assignees = List.from(inviteMembersListController.fullSelectedMembers);
      }
      selectedMemberIds = null;
    }

    final List<OrganizationDepartment> departments =
        List.from(departmentController.selectedDepartment.value);
    final List<OrganizationRole> roles =
        List.from(roleController.selectedRoles.value);
    final List<Conversation> threads =
        List.from(groupController.selectedItems.value);
    final List<ChatBotModel> bots =
        List.from(botController.selectedItemsNoDuplicate);
    final List<GPWorkspace> workspaces =
        List.from(workspaceController.selectedItemsNoDuplicate);
    final List<Participant> ignoreParticipants =
        List.from(groupController.ignoreParticipants);

    // bỏ ids trong list not removable
    if (!svo.isReturnNotRemovableList()) {
      svo.notRemovableUserIds?.forEach((id) {
        assignees.removeWhere((e) => e.id == id);
      });
      svo.notRemovableMemberIds?.forEach((id) {
        assignees.removeWhere((e) => e.id == id);
      });

      // thread
      svo.notRemovableThreadIds?.forEach((id) {
        threads.removeWhere((e) => e.id.toString() == id);
      });

      // department
      svo.notRemovableDepartmentIds?.forEach((id) {
        departments.removeWhere((e) => e.id == id);
      });

      // role
      svo.notRemovableRoleIds?.forEach((id) {
        roles.removeWhere((e) => e.id == id);
      });
    }

    final result = SelectInviteesResult(
      selectedMembers: assignees,
      selectedDepartments: departments,
      selectedMemberIds: selectedMemberIds,
      selectedRoles: roles,
      selectedThreads: threads,
      bots: bots,
      workspaces: workspaces,
      ignoreParticipants: ignoreParticipants,
      hasChangeThread: groupController.hasChangeSelected,
      hasChangeDepartment: departmentController.hasChangeSelected,
      hasChangeRole: roleController.hasChangeSelected,
    );

    /// added by admin
    if (svo.addedMyAddminIds != null) {
      List<Assignee> membersAddedByAdmin = assignees
          .where((e) => svo.membersAddedByAdminIds?.contains(e.id) ?? false)
          .toList();
      List<Conversation> threadsAddedByAdmin = threads
          .where((e) =>
              svo.threadAddedByAdminIds?.contains(e.id.toString()) ?? false)
          .toList();
      List<OrganizationDepartment> departmentsAddedByAdmin = departments
          .where((e) => svo.departmentAddedByAdminIds?.contains(e.id) ?? false)
          .toList();
      List<OrganizationRole> rolesAddedByAdmin = roles
          .where((e) => svo.roleAddedByAdminIds?.contains(e.id) ?? false)
          .toList();

      result.selectedMembers?.removeWhere(
          (e) => svo.membersAddedByAdminIds?.contains(e.id) ?? false);
      result.selectedThreads?.removeWhere(
          (e) => svo.threadAddedByAdminIds?.contains(e.id.toString()) ?? false);
      result.selectedDepartments?.removeWhere((e) =>
          svo.departmentAddedByAdminIds?.contains(e.id.toString()) ?? false);
      result.selectedRoles?.removeWhere(
          (e) => svo.roleAddedByAdminIds?.contains(e.id) ?? false);

      result.setAddedByAdminListData(
        membersAddedByAdmin: membersAddedByAdmin,
        threadsAddedByAdmin: threadsAddedByAdmin,
        departmentsAddedByAdmin: departmentsAddedByAdmin,
        rolesAddedByAdmin: rolesAddedByAdmin,
      );
    }
    // Trường hợp là search user để đối chiếu lịch
    // cần bắn về native để load lại calendar

    logDebug("result -> ${jsonEncode(result.toJson())}");

    if (title.value == LocaleKeys.calendar_calendar_comparison.tr) {
      await PlatformNavigator.pop(assignees.map((e) => e.toJson()).toList(),
          result: result);
    } else if (fromNative) {
      if (onDoneMethodChannel) {
        final goBack = await MemberPickerDeeplink.onPressDone(result,
            groupId: groupId, name: onDoneMethodName);
        if (goBack) {
          MemberPickerDeeplink.saveAssigneePick(result);
          Utils.back();
        } else {
          return;
        }
      } else {
        MemberPickerDeeplink.saveAssigneePick(result);
        Utils.back();
      }
    } else {
      Get.back(result: result, closeOverlays: true);
    }
  }

  // fill các selected object
  void _fillSelectedObjects(SelectInviteesOptions options) {
    inviteMembersListController.selectedItems
        .add(options.selectedMembers ?? []);
    inviteMembersListController.fullSelectedMembers
        .addAll(options.selectedMembers ?? []);
    departmentController.selectedDepartment
        .add(options.selectedDepartments ?? []);
    roleController.selectedRoles.add(options.selectedRoles ?? []);
    groupController.selectedItems.add(options.selectedThreads ?? []);
    groupController.ignoreParticipants = options.ignoreUsers ?? [];
    botController.setSelectedItems(options.selectedBots);
  }

  // fill thêm selected members bằng selected ids
  void _fillSelectedObjectWithListIds(SelectInviteesOptions args) async {
    if (hasTabMember) {
      try {
        if (args.selectedMemberIds?.isNotEmpty ?? false) {
          inviteMembersListController
              .setSelectedMemberIds(args.selectedMemberIds ?? []);
        }
        // // fill cả những ignoreRemoveMember
        if (args.needSyncIgnoreRemoveMembers) {
          final ignoreRemoveUserIds = args.notRemovableUserIds ?? [];
          if (ignoreRemoveUserIds.isNotEmpty) {
            final userInfoApi = UserInfoApi();
            final users = await userInfoApi.getListUser(ignoreRemoveUserIds);
            inviteMembersListController.addIfNotExistValue(users.data ?? []);
          }
        }
      } catch (e, s) {
        handleError(e, s);
      }
    }

    // departments
    if (hasTabDepartment) {
      try {
        final service = OrganizationAPI();
        final results = await Future.wait([service.getDepartments()]);
        final deptsResponse = results.first;
        _tempDeptsFlat = [];
        final deps = _flatDepartmentResponse(
            deptsResponse); // List<OrganizationDepartment>.from(_flatDeptOne(deptsResponse));
        final selected = deps
            .where((element) =>
                args.selectedDepartmentIds?.contains(element.id) ?? false)
            .toSet()
            .toList();
        if (selected.isNotEmpty) {
          departmentController.selectedDepartment.add(selected);
          if (args.tabs.length == 1 &&
              (args.tabs.contains(SelectInviteeTabs.department))) {
            departmentController.fillSelectedItems();
          }
        }
      } catch (e, s) {
        handleError(e, s);
      }
    }

    if (hasTabRole) {
      try {
        final service = OrganizationAPI();
        final results = await Future.wait([service.getRoles()]);
        // roles
        final listRoles = results.first;
        final selectedRoles = listRoles
            .where((element) =>
                args.selectedRoleIds?.contains(element.id) ?? false)
            .toList();
        if (selectedRoles.isNotEmpty) {
          roleController.selectedRoles.add(selectedRoles);
        }
      } catch (e, s) {
        handleError(e, s);
      }
    }

    if (hasTabGroup) {
      // groups
      try {
        final toThreadIds =
            args.selectedThreadIds ?? args.threadAddedByAdminIds;
        if (toThreadIds != null && toThreadIds.isNotEmpty) {
          final chatService = ChatAPI();
          final getThreadApis = toThreadIds.map((e) async {
            try {
              return await chatService.getConversation(
                  id: int.tryParse(e) ?? 0);
            } catch (error) {
              return Conversation(id: int.tryParse(e) ?? 0);
            }
          }).toList();
          final threads = await Future.wait(getThreadApis);
          if (threads.isNotEmpty) {
            groupController.selectedItems.add(threads);
          }
        }
      } catch (e, s) {
        handleError(e, s);
      }
    }

    if (hasTabBot) {
      // bots
      botController.setSelectedItemsByIds(args.selectedBotIds);
      botController.init();
    }

    if (hasTabWorkspace) {
      // workspaces
      workspaceController.setSelectedItemsByIds(args.selectedWorkspaceIds);
      workspaceController.init();
    }

    // ignore participants
    final ignoreUserIds = args.ignoreUserIds;
    if (ignoreUserIds != null && ignoreUserIds.isNotEmpty) {
      groupController.ignoreParticipants
          .addAll(ignoreUserIds.map((e) => Participant(id: e.toString())));
    }

    checkHasAnySelectedItem();
  }

  List<OrganizationDepartment> _flatDepartmentResponse(
      OrganizationDepartmentResponse response) {
    _tempDeptsFlat.addAll(response.data);
    for (var item in response.data) {
      _flatDeptOne(item);
    }
    return _tempDeptsFlat;
  }

  List<OrganizationDepartment> _flatDeptOne(
      OrganizationDepartment organizationDepartment) {
    _tempDeptsFlat.add(organizationDepartment);
    for (var item in organizationDepartment.children) {
      _flatDeptOne(item);
    }
    return _tempDeptsFlat;
  }

  void pushToSelectedPage() {
    final controller =
        InviteesSelectedListController(selectedMemberIds.toList());
    Get.to(() => InviteesSelectedListPage(controller));
  }

  @override
  void initArguments(SelectInviteesOptions args) {
    svo = args;

    inviteMembersListController.initArguments(args);
    groupController.initArguments(args);
    departmentController.initArguments(args);
    roleController.initArguments(args);
    botController.initArguments(args);
    workspaceController.initArguments(args);
  }

  void hideTooltip() {
    inviteMembersListController.hideTooltip();
    inviteMembersListController.hideSelectedTooltip();

    groupController.hideTooltip();
    groupController.hideSelectedTooltip();

    departmentController.hideTooltip();
    departmentController.hideSelectedTooltip();

    roleController.hideTooltip();
    roleController.hideSelectedTooltip();
  }
}

mixin _TabMixin {
  final RxList<SelectInviteeTabs> tabs = <SelectInviteeTabs>[].obs;

  late final TabController tabController;

  bool get hasTabMember => tabs.contains(SelectInviteeTabs.member);
  bool get hasTabGroup => tabs.contains(SelectInviteeTabs.group);
  bool get hasTabDepartment => tabs.contains(SelectInviteeTabs.department);
  bool get hasTabRole => tabs.contains(SelectInviteeTabs.role);
  bool get hasTabBot => tabs.contains(SelectInviteeTabs.bot);
  bool get hasTabWorkspace => tabs.contains(SelectInviteeTabs.workspace);
}

import 'package:gp_core/base/networking/services/workspace_api.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/gp_workspace.dart';
import 'package:gp_core/models/orgchat/select_invitees_model.dart';
import 'package:gp_feat_member_picker/base/base_picker_controller.dart';

import '../invitees_list/widget/limit_dialog.dart';
import '../picker_mixin.dart';
import '../selected_list/select_invitees_controller.dart';

const kBotListWidget = "kBotListWidget";

class WorkspaceController extends BasePickerController<GPWorkspace>
    with ArgumentConfigMixin
    implements ArgumentInitialMixin {
  WorkspaceController({
    required this.onDataPicked,
  });

  final WorkSpaceAPI api = WorkSpaceAPI();

  final OnDataPicked onDataPicked;

  bool get isPickMultiple =>
      svo.selectInviteesPickMode == SelectInviteesPickMode.pickMultiple;

  bool get isViewOnly => svo.mode == SelectInviteesOptionsMode.viewOnly;

  @override
  Future fetchData() async {
    final response = await api.workspaces();

    // handleLinks(response);

    return response.data;
  }

  @override
  void initArguments(SelectInviteesOptions arg) {
    svo = arg;
  }

  @override
  void onSelected(GPWorkspace item) {
    // Kiểm tra giới hạn chọn cho tab workspace khi đang chọn
    if (item.rxIsSelected.value) {
      final tabLimit = SelectInviteeTabs.workspace.getMaxSelectionLimit(svo);
      if (tabLimit != null && selectedItems.length >= tabLimit) {
        // Bỏ chọn lại item vì đã đạt giới hạn
        item.rxIsSelected.value = false;

        Popup.instance.showBottomSheet(LimitDialog(
            title: "Đã đạt giới hạn chọn ${SelectInviteeTabs.workspace.title}",
            content:
                "Bạn chỉ có thể chọn tối đa $tabLimit ${SelectInviteeTabs.workspace.title.toLowerCase()}"));
        return;
      }
    }

    super.onSelected(item);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();
  }
}

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_member_picker/picker_mixin.dart';
import 'package:gp_feat_member_picker/roles/version/1.0.6.dart';
import 'package:gp_feat_member_picker/selected_list/select_invitees_controller.dart';

import '../invitees_list/widget/limit_dialog.dart';
import '../version/tooltip_mixin.dart';

class OrganizationRoleSelectListController
    with
        OrgChatConfigTab,
        ArgumentConfigMixin,
        TooltipMixin,
        HasChangeSelectedFlag
    implements ArgumentInitialMixin {
  final _service = OrganizationAPI();

  final displayRoles = BehaviorSubject<List<OrganizationRole>>.seeded([]);
  final roles = BehaviorSubject<List<OrganizationRole>>.seeded([]);
  final selectedRoles = BehaviorSubject<List<OrganizationRole>>.seeded([]);

  final searchTextEditingController = TextEditingController();

  bool isLoading = false;

  void setOnPickModeListener(Function onPickOneCallback) {
    this.onPickOneCallback = onPickOneCallback;
  }

  void initState() {
    _getRoles();

    roles.listen((value) {
      _updateDisplayRolesWithSearchKeyword(null);
    });

    searchTextEditingController.addListener(() {
      final text = searchTextEditingController.text;
      searchTextValueChanged(text);
    });
  }

  void _getRoles() async {
    if (roles.value.isNotEmpty) {
      return;
    }
    try {
      isLoading = true;
      final list = await _service.getRoles();
      roles.value.addAll(list);
      roles.add(roles.value);

      // fill selected items
      if (selectedRoles.value.isNotEmpty) {
        try {
          var _selected = roles.value
              .where((element) => selectedRoles.value.contains(element))
              .toList();
          if (_selected.isNotEmpty) {
            for (var element in _selected) {
              element.isSelected = true;
            }
            selectedRoles.add(_selected);
          }
        } catch (e) {
          isLoading = false;
          logDebug(e);
        }
      }

      _updateNotRemovable();

      isLoading = false;
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.memberPicker._getRoles',
        data: {'error': e, 'stacktrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.memberPicker._getRoles',
        trackerType: GPTrackerType.memberPicker,
      );

      isLoading = false;
      logDebug(trace);
      handleErrorDefault(e, trace);
    }
  }

  void onRemove(int index) {
    selectedRoles.value[index].isSelected = false;

    roles.add(roles.value);

    selectedRoles.value.removeAt(index);
    selectedRoles.add(selectedRoles.value);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();

    hasChangeSelected = true;
  }

  void removeAllSelected() {
    for (var role in selectedRoles.value) {
      role.isSelected = false;
    }
    roles.add(roles.value);
    selectedRoles.add([]);

    hasChangeSelected = true;
  }

  void onSelectionChanged(OrganizationRole role) {
    // Kiểm tra giới hạn chọn cho tab role khi đang chọn
    if (role.isSelected) {
      final tabLimit = SelectInviteeTabs.role.getMaxSelectionLimit(svo);
      if (tabLimit != null && selectedRoles.value.length >= tabLimit) {
        // Bỏ chọn lại role vì đã đạt giới hạn
        role.isSelected = false;
        roles.add(roles.value);

        Popup.instance.showBottomSheet(LimitDialog(
            title: "Đã đạt giới hạn chọn ${SelectInviteeTabs.role.title}",
            content:
                "Bạn chỉ có thể chọn tối đa $tabLimit ${SelectInviteeTabs.role.title.toLowerCase()}"));
        return;
      }

      selectedRoles.value.add(role);
      onPickOneCallback?.call();
    } else {
      selectedRoles.value.remove(role);
    }
    selectedRoles.add(selectedRoles.value);
    roles.add(roles.value);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();

    hasChangeSelected = true;
  }

  void searchTextValueChanged(String text) {
    _updateDisplayRolesWithSearchKeyword(text);
  }

  String _keyword = '';
  void _updateDisplayRolesWithSearchKeyword(String? keyword) {
    if (keyword != null) {
      _keyword = keyword;
    }
    final values = roles.value
        .where(
          (element) => TiengViet.parse(element.name ?? '')
              .toLowerCase()
              .contains(TiengViet.parse(_keyword).toLowerCase()),
        )
        .toList();
    displayRoles.add(values);
  }

  void _updateNotRemovable() {
    void updateNotRemovableList(
      List<String>? input, {
      Function(OrganizationRole)? handleEachItem,
    }) {
      if (input?.isNotEmpty == true) {
        for (var e in input!) {
          final role = roles.value
              .firstWhereOrNull((element) => element.id.toString() == e);

          if (role != null) {
            role.isSelected = true;
            role.isEnabled = false;

            if (!selectedRoles.value.contains(role)) {
              selectedRoles.value.add(role);
            }

            handleEachItem?.call(role);
          }
        }

        selectedRoles.add(selectedRoles.value);
      }
    }

    if (svo.mode == SelectInviteesOptionsMode.viewOnly) {
      updateNotRemovableList(svo.selectedRoleIds);

      displayRoles.value.clear();
      displayRoles.value.addAll(selectedRoles.value);

      roles.value.clear();
      roles.value.addAll(selectedRoles.value);
    } else {
      updateNotRemovableList(svo.notRemovableRoleIds);

      // thread được thêm bởi admin không thể bỏ chọn
      updateNotRemovableList(svo.roleAddedByAdminIds, handleEachItem: (p0) {
        addTooltip(p0);
      });
    }

    sortAddedByAdminList(displayRoles.value);
    sortAddedByAdminList(roles.value);

    displayRoles.add(displayRoles.value);

    roles.add(roles.value);
  }

  @override
  void initArguments(SelectInviteesOptions arg) {
    svo = arg;
  }

  void sortAddedByAdminList(List<OrganizationRole> input) {
    if (svo.roleAddedByAdminIds?.isNotEmpty == true) {
      input.sort(
        (a, b) => a.isEnabled == false ? 0 : 1,
      );
    }
  }
}

void handleErrorDefault(Object error, StackTrace s) {
  logDebug("got error ${error.toString()}, $s");
  var message = 'Có lỗi xảy ra, vui lòng thử lại!';
  if (error is AppException) {
    message = error.toString();
  }
  Popup.instance.showSnackBar(message: message, type: SnackbarType.error);
}

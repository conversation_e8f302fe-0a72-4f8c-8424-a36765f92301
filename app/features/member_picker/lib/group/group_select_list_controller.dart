import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_member_picker/group/version/1.0.6.dart';
import 'package:gp_feat_member_picker/picker_mixin.dart';
import 'package:gp_feat_member_picker/selected_list/select_invitees_controller.dart';

import '../invitees_list/widget/limit_dialog.dart';
import '../version/tooltip_mixin.dart';
import 'group_member_list_controller.dart';
import 'group_member_list_page.dart';

class GroupSelectListController
    with
        OrgChatConfigTab,
        ArgumentConfigMixin,
        TooltipMixin,
        HasChangeSelectedFlag
    implements ArgumentInitialMixin {
  final _service = ChatAPI();
  int? _lastId;
  final int _limit = 100;

  List<Conversation> _sourceConversations = [];
  final displayItems = BehaviorSubject<List<Conversation>>.seeded([]);
  final selectedItems = BehaviorSubject<List<Conversation>>.seeded([]);
  List<Participant> ignoreParticipants = [];

  final isLoading = BehaviorSubject.seeded(false);

  final scrollController = ScrollController();

  Timer? _debounce;

  final searchTextEditingController = TextEditingController();

  void setOnPickModeListener(Function onPickOneCallback) {
    this.onPickOneCallback = onPickOneCallback;
  }

  void initState() {
    getListItems(reset: true);

    searchTextEditingController.addListener(() {
      final text = searchTextEditingController.text;
      searchTextValueChanged(text);
    });
  }

  void dispose() {
    _debounce?.cancel();
  }

  Future<bool> getListItems({bool reset = false}) async {
    if (reset) {
      _lastId = null;
    }
    try {
      isLoading.value = true;
      final result =
          await _service.getListConversation(lastId: _lastId, pageSize: _limit);
      final list = result
          .where((element) => element.partner?.notBotOrSystem ?? true)
          .where((element) => element.type != 'direct')
          .toList();
      if (reset) {
        _sourceConversations = list;
      } else {
        _sourceConversations.addAll(list);
      }
      displayItems.value = _sourceConversations;

      // fill selected items
      if (selectedItems.value.isNotEmpty) {
        try {
          var selected = _sourceConversations
              .toSet()
              .intersection(selectedItems.value.toSet())
              .toList();
          if (selected.isNotEmpty) {
            for (var element in selected) {
              element.isSelected.value = true;
            }
            selectedItems.add(selected);
          }
        } catch (e, s) {
          GPCoreTracker().appendError(
            'Flutter.memberPicker.group.getListItems',
            data: {'error': e, 'stacktrace': s},
          );
          GPCoreTracker().sendLog(
            message: 'Flutter.memberPicker.group.getListItems',
            trackerType: GPTrackerType.calendar,
          );

          isLoading.value = false;
          logDebug(e);
        }
      }

      if (result.isNotEmpty) {
        _lastId = result.last.id;
        await getListItems();
        return true;
      }

      _updateNotRemovable();

      isLoading.value = false;
      return false;
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.memberPicker.group.getListItems',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.memberPicker.group.getListItems',
        trackerType: GPTrackerType.memberPicker,
      );

      isLoading.value = false;
      logDebug(e);
      logDebug(s);
      return false;
    }
  }

  void onConversationUpdated(Conversation p1) {
    // Kiểm tra giới hạn chọn cho tab group khi đang chọn
    if (!p1.isSelected.value) {
      final tabLimit = SelectInviteeTabs.group.getMaxSelectionLimit(svo);
      if (tabLimit != null && selectedItems.value.length >= tabLimit) {
        Popup.instance.showBottomSheet(LimitDialog(
            title: "Đã đạt giới hạn chọn ${SelectInviteeTabs.group.title}",
            content:
                "Bạn chỉ có thể chọn tối đa $tabLimit ${SelectInviteeTabs.group.title.toLowerCase()}"));
        return;
      }
    }

    p1.isSelected.value = !p1.isSelected.value;
    displayItems.add(displayItems.value);

    // selected section
    if (p1.isSelected.value) {
      selectedItems.value.add(p1);
    } else {
      final index = selectedItems.value.indexWhere((element) => element == p1);
      if (index >= 0) {
        selectedItems.value.removeAt(index);
      }
    }
    selectedItems.add(selectedItems.value);

    onPickOneCallback?.call();
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();

    hasChangeSelected = true;
  }

  void onRemoveSelectedItem(Conversation conversation) {
    conversation.isSelected.value = false;

    displayItems.add(displayItems.value);

    selectedItems.value.remove(conversation);
    selectedItems.add(selectedItems.value);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();

    hasChangeSelected = true;
  }

  void removeAllSelected() {
    for (var conversation in selectedItems.value) {
      conversation.isSelected.value = false;
    }
    displayItems.add(displayItems.value);
    selectedItems.add([]);

    hasChangeSelected = true;
  }

  void toListDetail() async {
    final controller =
        GroupMemberListController(selectedItems.value, ignoreParticipants);
    final page = GroupMemberListPage(controller);
    final result = await Get.to(() => page);
    if (result is List<Participant>) {
      ignoreParticipants = result;
    }
  }

  void searchTextValueChanged(String text) {
    // dùng debouce + compute cho heavy task
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      final keyword = text;
      final input = [_sourceConversations, keyword];
      final result = _filter(input);
      // định dùng compute nhưng chỗ này có 3 list dùng chung instance của Conversation
      // compute nó trả ra instance khác nên hơi khó xử lý
      // TODO: dùng compute đoạn này
      // final result = await compute(_filter, input);
      displayItems.value = result;
    });
  }

  List<Conversation> _filter(List list) {
    final source = list[0] as List<Conversation>;
    final keyword = list[1] as String;
    if (keyword.isNotEmpty) {
      final result = source
          .where((element) => (TiengViet.parse(element.name ?? ''))
              .toLowerCase()
              .contains(TiengViet.parse(keyword).toLowerCase()))
          .toList();
      return result;
    } else {
      return source;
    }
  }

  void _updateNotRemovable() {
    void updateNotRemovableList(
      List<String>? input, {
      Function(Conversation)? handleEachItem,
    }) {
      if (input?.isNotEmpty == true) {
        for (var e in input!) {
          final c = _sourceConversations
              .firstWhereOrNull((element) => element.id.toString() == e);

          if (c != null) {
            c.isSelected.value = true;
            c.isEnabled = false;

            if (!selectedItems.value.contains(c)) {
              selectedItems.value.add(c);
            }

            handleEachItem?.call(c);
          }
        }
      }
    }

    if (svo.mode == SelectInviteesOptionsMode.viewOnly) {
      updateNotRemovableList(svo.selectedThreadIds);

      _sourceConversations.clear();
      _sourceConversations.addAll(selectedItems.value);

      displayItems.value.clear();
      displayItems.value.addAll(selectedItems.value);
    } else {
      updateNotRemovableList(svo.notRemovableThreadIds);

      // thread được thêm bởi admin không thể bỏ chọn
      updateNotRemovableList(svo.threadAddedByAdminIds, handleEachItem: (p0) {
        addTooltip(p0);
      });
    }

    sortAddedByAdminList(selectedItems.value);
    sortAddedByAdminList(displayItems.value);

    selectedItems.add(selectedItems.value);

    displayItems.add(displayItems.value);
  }

  void sortAddedByAdminList(List<Conversation> input) {
    if (svo.threadAddedByAdminIds?.isNotEmpty == true) {
      input.sort(
        (a, b) => a.isEnabled == false ? 0 : 1,
      );
    }
  }

  @override
  void initArguments(SelectInviteesOptions arg) {
    svo = arg;
  }
}

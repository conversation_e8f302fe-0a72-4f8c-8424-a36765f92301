import 'package:gp_core/core.dart';
import 'package:gp_core/models/bot/bot_response.dart';

import '../gp_workspace.dart';

class SelectInviteesResult with _ResultV1_0_6 {
  final List<Assignee>? selectedMembers;
  final List<String>? selectedMemberIds;
  final List<OrganizationDepartment>? selectedDepartments;
  final List<OrganizationRole>? selectedRoles;
  final List<Conversation>? selectedThreads;
  final List<Participant>? ignoreParticipants;
  final List<ChatBotModel>? bots;
  final List<GPWorkspace>? workspaces;
  final bool? hasChangeThread;
  final bool? hasChangeDepartment;
  final bool? hasChangeRole;

  SelectInviteesResult({
    this.selectedDepartments,
    this.selectedMembers,
    required this.selectedMemberIds,
    this.selectedRoles,
    this.selectedThreads,
    this.ignoreParticipants,
    this.bots,
    this.workspaces,
    this.hasChangeThread,
    this.hasChangeDepartment,
    this.hasChangeRole,
  });

  Map<String, dynamic> toJson() => {
        "selected_assignees":
            List<dynamic>.from(selectedMembers?.map((e) => e.toJson()) ?? []),
        "selected_assignee_ids": List<dynamic>.from(
            selectedMemberIds?.map((e) => e.toString()) ?? []),
        "selected_departments": List<dynamic>.from(
            selectedDepartments?.map((e) => e.toJson()) ?? []),
        "selected_roles":
            List<dynamic>.from(selectedRoles?.map((e) => e.toJson()) ?? []),
        "selected_threads":
            List<dynamic>.from(selectedThreads?.map((e) => e.toJson()) ?? []),
        "selected_bots": List<dynamic>.from(
          bots?.map((e) => e.toJson()) ?? [],
        ),
        "selected_workspaces": List<dynamic>.from(
          workspaces?.map((e) => e.toJson()) ?? [],
        ),
      }..addAll(_addedByAdminToJson());
}

class SelectInviteesOptions with V1_0_1, _V1_0_6, _V1_0_7 {
  final List<Assignee>? selectedMembers;
  final List<OrganizationDepartment>? selectedDepartments;
  final List<OrganizationRole>? selectedRoles;
  final List<Conversation>? selectedThreads;
  final List<Participant>? ignoreUsers;

  final List<int>? selectedMemberIds;
  final List<String>? selectedDepartmentIds;
  final List<String>? selectedRoleIds;
  final List<String>? selectedThreadIds;
  final List<String>? selectedBotIds;
  final List<String>? selectedWorkspaceIds;
  final List<int>? ignoreUserIds;
  final List<int>? notRemovableUserIds;

  final List<SelectInviteeTabs> tabs = [];
  final String? title;
  final String? actionButtonTitle;
  final bool? onlyCurrentWorkspace;
  bool? fromNative;

  /// Chế độ hiển thị
  final SelectInviteesViewMode viewMode;

  final SelectInviteesOptionsMode mode;

  /// filter những member có id này sẽ k xuất hiện để select/unselect
  final List<int>? filterOutMemberIds;

  //
  final List<ChatBotModel>? selectedBots;

  //
  final List<GPWorkspace>? selectedWorkspaces;

  /// Calendar BE ko còn trả về full attendees, cần gọi lấy hết để ko bị lỗi
  /// khi save
  final bool? needGetAllSelectedMembers;

  Function()? onBackPressed;

  /// nếu truyền workspaceId, sẽ replace vào biến 'x-gapo-workspace-id' ở header request
  final String? workspaceId;

  /// Giới hạn số lượng item có thể chọn cho từng tab
  /// Key: tên tab (member, department, role, group, bot, workspace)
  /// Value: số lượng tối đa có thể chọn
  final Map<String, int>? tabSelectionLimits;

  SelectInviteesOptions({
    this.title,
    this.actionButtonTitle,
    List<SelectInviteeTabs>? tabs,
    this.selectedDepartments,
    this.selectedMembers,
    this.selectedRoles,
    this.selectedThreads,
    this.selectedBots,
    this.selectedWorkspaces,
    this.ignoreUsers,
    this.ignoreUserIds,
    this.notRemovableUserIds,
    this.selectedDepartmentIds,
    this.selectedMemberIds,
    this.selectedRoleIds,
    this.selectedThreadIds,
    this.selectedBotIds,
    this.selectedWorkspaceIds,
    this.onlyCurrentWorkspace,
    this.viewMode = SelectInviteesViewMode.screen,
    this.mode = SelectInviteesOptionsMode.selectNewWithValue,
    this.filterOutMemberIds,
    this.fromNative = false,
    Map<String, dynamic>? arguments,
    Map<String, dynamic>? sConfigs,
    Map<String, dynamic>? memberConfigs,
    SelectInviteesPickMode selectInviteesPickMode =
        SelectInviteesPickMode.pickMultiple,
    Map<String, dynamic>? notRemovable,
    Map<String, dynamic>? addedMyAddminIds,
    this.needGetAllSelectedMembers,
    this.onBackPressed,
    this.workspaceId,
    this.tabSelectionLimits,
  }) {
    this.arguments = arguments;
    this.sConfigs = sConfigs;
    this.memberConfigs = memberConfigs;
    this.selectInviteesPickMode = selectInviteesPickMode;
    this.notRemovable = notRemovable;
    this.addedMyAddminIds = addedMyAddminIds;

    if (tabs?.isNotEmpty == true) {
      this.tabs.addAll(tabs!);
    } else {
      this.tabs.addAll(
        [
          SelectInviteeTabs.member,
          SelectInviteeTabs.group,
          SelectInviteeTabs.department,
          SelectInviteeTabs.role,
        ],
      );
    }

    initNotRemovableData();

    initAddedByAdminData();
  }

  bool get showCheckBox =>
      selectInviteesPickMode != SelectInviteesPickMode.pickOne;

  factory SelectInviteesOptions.fromJson(Map<String, dynamic> json) {
    final Iterable? smIds = json['selected_member_ids'];
    final Iterable? iuIds = json['ignored_user_ids'];
    final Iterable? iruIds = json['not_removable_user_ids'];
    final Iterable? fuIds = json['filter_out_member_ids'];

    final List<String> listMemberIds = smIds != null
        ? List.from(smIds.where((e) => e != null && e != ""))
        : [];
    final List<String> listIgnoreUserIds = iuIds != null
        ? List.from(iuIds.where((e) => e != null && e != ""))
        : [];
    final List<String> listNotRemovableUserIds = iruIds != null
        ? List.from(iruIds.where((e) => e != null && e != ""))
        : [];
    final List<String> listFilterOutMemberIds = fuIds != null
        ? List.from(fuIds.where((e) => e != null && e != ""))
        : [];

    return SelectInviteesOptions(
      title:
          json['title'] ?? LocaleKeys.calendar_select_invitees_title_invite.tr,
      actionButtonTitle:
          json['action_button_title'] ?? LocaleKeys.calendar_done.tr,
      viewMode: SelectInviteesViewMode.values.byName(
          ParserHelper.parseString(json['view_model']) ??
              SelectInviteesViewMode.screen.name),
      mode: SelectInviteesOptionsMode.values.byName(
          ParserHelper.parseString(json['mode']) ??
              SelectInviteesOptionsMode.selectNewWithValue.name),
      tabs: json["tabs"] == null
          ? null
          : (json["tabs"] as List)
              .map((e) => SelectInviteeTabs.values
                  .byName(ParserHelper.parseString(e) ?? ""))
              .toList(),
      selectedMembers: json['selected_members'] == null
          ? null
          : (json['selected_members'] as List)
              .map((e) => Assignee.fromJson(e))
              .toList(),
      selectedDepartments: json['selected_departments'] == null
          ? null
          : (json['selected_departments'] as List)
              .map((e) => OrganizationDepartment.fromJson(e))
              .toList(),
      selectedRoles: json['selected_roles'] == null
          ? null
          : (json['selected_roles'] as List)
              .map((e) => OrganizationRole.fromJson(e))
              .toList(),
      selectedThreads: json['selected_threads'] == null
          ? null
          : (json['selected_threads'] as List)
              .map((e) => Conversation.fromJson(e))
              .toList(),
      selectedBots: json['selected_bots'] == null
          ? null
          : (json['selected_bots'] as List)
              .map((e) => ChatBotModel.fromJson(e))
              .toList(),
      ignoreUsers: json['ignore_users'] == null
          ? null
          : (json['ignore_users'] as List)
              .map((e) => Participant.fromJson(e))
              .toList(),
      selectedMemberIds: List<int>.from(listMemberIds.map((e) => int.parse(e))),
      selectedDepartmentIds: json['selected_department_ids'] == null
          ? null
          : List<String>.from(json['selected_department_ids']),
      selectedThreadIds: json['selected_thread_ids'] == null
          ? null
          : List<String>.from(json['selected_thread_ids']),
      selectedRoleIds: json['selected_role_ids'] == null
          ? null
          : List<String>.from(json['selected_role_ids']),
      selectedBotIds: json['selected_bot_ids'] == null
          ? null
          : List<String>.from(json['selected_bot_ids']),
      selectedWorkspaceIds: json['selected_workspace_ids'] == null
          ? null
          : List<String>.from(json['selected_workspace_ids']),
      ignoreUserIds: List<int>.from(listIgnoreUserIds.map((e) => int.parse(e))),
      notRemovableUserIds:
          List<int>.from(listNotRemovableUserIds.map((e) => int.parse(e))),
      arguments: json['arguments'] != null
          ? Map<String, dynamic>.from(json['arguments'])
          : null,
      selectInviteesPickMode: SelectInviteesPickMode.values.byName(
          ParserHelper.parseString(json['pick_mode']) ??
              SelectInviteesPickMode.pickMultiple.name),
      sConfigs: json['s_configs'] != null
          ? Map<String, dynamic>.from(json['s_configs'])
          : null,
      memberConfigs: json['member_configs'] != null
          ? Map<String, dynamic>.from(json['member_configs'])
          : null,
      filterOutMemberIds:
          List<int>.from(listFilterOutMemberIds.map((e) => int.parse(e))),
      notRemovable: json['not_removable'] != null
          ? Map<String, dynamic>.from(json['not_removable'])
          : null,
      addedMyAddminIds: json['added_by_admin_ids'] != null
          ? Map<String, dynamic>.from(json['added_by_admin_ids'])
          : null,
      workspaceId: json['workspace_id'],
      tabSelectionLimits: json['tab_selection_limits'] != null
          ? Map<String, int>.from(json['tab_selection_limits'])
          : null,
    );
  }
}

enum SelectInviteesViewMode {
  screen,
  bottomSheet,
  dialog,
}

// --------- \\
enum SelectInviteesOptionsMode {
  /// Dùng khi thêm mới, khi chọn lần đầu
  selectNew,

  /// Dùng khi thêm mới, khi chọn lần 2, 3,... mode này cần fill các list selected object
  /// [selectedMembers], [selectedDepartments], [selectedRoles], [selectedThreads]
  /// không cần fill các loại ids, vì là local data không liên quan đến BE
  selectNewWithValue,

  /// Dùng khi edit, cần pass các loại object
  /// như selectNewWithValue và thêm [selectedMemberIds]
  edit,

  /// Khi chỉ thêm, ví dụ như event attendee thêm người khác vào event,
  /// không thể xóa các value đã added
  /// với mode này chỉ cần pass [selectedMemberIds],
  /// không cần các selected value hay selected ids khác
  addOnly,

  /// Chỉ cho phép user xem, không cho edit, add
  viewOnly
}

// --------- \\
enum SelectInviteesPickMode {
  pickOne,
  pickMultiple,
}

extension SelectInviteesPickModeLogic on SelectInviteesPickMode {
  bool get isPickOne => this == SelectInviteesPickMode.pickOne;
}

// --------- \\
enum SelectInviteeTabs { member, department, role, group, bot, workspace }

extension TabTitle on SelectInviteeTabs {
  String get title {
    switch (this) {
      case SelectInviteeTabs.member:
        return LocaleKeys.calendar_select_invitees_tab_title_member.tr;
      case SelectInviteeTabs.department:
        return LocaleKeys.calendar_select_invitees_tab_title_department.tr;
      case SelectInviteeTabs.role:
        return LocaleKeys.calendar_select_invitees_tab_title_role.tr;
      case SelectInviteeTabs.group:
        return LocaleKeys.calendar_select_invitees_tab_title_group.tr;
      case SelectInviteeTabs.bot:
        return LocaleKeys.calendar_select_invitees_tab_title_bot.tr;
      case SelectInviteeTabs.workspace:
        return LocaleKeys.calendar_select_invitees_tab_title_workspace.tr;
    }
  }
}

/// Extension để lấy giới hạn số lượng item có thể chọn cho từng tab
extension TabSelectionLimit on SelectInviteeTabs {
  /// Lấy giới hạn số lượng item có thể chọn từ SelectInviteesOptions
  int? getMaxSelectionLimit(SelectInviteesOptions options) {
    final tabLimits = options.tabSelectionLimits;
    if (tabLimits == null) return null;

    switch (this) {
      case SelectInviteeTabs.member:
        return tabLimits['member'];
      case SelectInviteeTabs.department:
        return tabLimits['department'];
      case SelectInviteeTabs.role:
        return tabLimits['role'];
      case SelectInviteeTabs.group:
        return tabLimits['group'];
      case SelectInviteeTabs.bot:
        return tabLimits['bot'];
      case SelectInviteeTabs.workspace:
        return tabLimits['workspace'];
    }
  }
}

extension SvoMemberIdsExt on SelectInviteesOptions {
  int? get maxMemberFullResponseReturnNumber {
    if (sConfigs == null ||
        !sConfigs!.containsKey("max_member_full_response_return_number")) {
      return null;
    }

    return sConfigs?["max_member_full_response_return_number"];
  }
}

/*
  V1.0.2: 
  - Thêm ignore_me
  - Thêm option ẩn hiện chọn tree_id
*/
extension SvoIgnoreMeExt on SelectInviteesOptions {
  /*
    true: bỏ current user_id khỏi danh sách members
    default value: true

    false: Hiển thị current user_id, thay tên = Tôi ở trên cùng của member list
  */
  bool get ignoreMe {
    if (arguments == null || !arguments!.containsKey("ignore_me")) {
      return true;
    }

    return arguments?["ignore_me"] ?? true;
  }

  int? get limitUsers {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["limit_users"] != null) {
      return sConfigs!["limit_users"];
    }
    return null;
  }

  /*
    true: cho phép đổi tree
    default value: false
  */
  bool get allowChangeTree {
    if (arguments == null || !arguments!.containsKey("allow_change_tree")) {
      return false;
    }

    return arguments?["allow_change_tree"] ?? false;
  }

  /*
    true: invoke method khi ấn back
    default value: false
  */
  bool get onBackMethodChannel {
    if (arguments == null || !arguments!.containsKey("on_back_press_method")) {
      return false;
    }

    return arguments?["on_back_press_method"] ?? false;
  }

  /// Invoke method khi ấn done
  ///
  /// default value: `false`
  bool get onDoneMethodChannel {
    if (arguments == null || !arguments!.containsKey("on_press_done_method")) {
      return false;
    }

    return arguments?["on_press_done_method"] ?? false;
  }

  /// Xoá danh sách đã chọn khi chuyển tab
  ///
  /// default value: `false`
  bool get removeAllSelectedOnTabChange {
    if (arguments == null ||
        !arguments!.containsKey("remove_all_selected_on_tab_change")) {
      return false;
    }

    return arguments?["remove_all_selected_on_tab_change"] ?? false;
  }

  /// Lọc danh sách user theo groupId
  String get groupId {
    if (arguments != null &&
        arguments!.isNotEmpty &&
        arguments!["group_id"] != null &&
        arguments!["group_id"] != "") {
      return arguments!["group_id"];
    }

    return "";
  }

  /// Lọc danh sách user theo threadId
  String get threadId {
    if (arguments != null &&
        arguments!.isNotEmpty &&
        arguments!["thread_id"] != null &&
        arguments!["thread_id"] != "") {
      return arguments!["thread_id"];
    }

    return "";
  }

  /// Android cần truyền thêm để phân biệt luồng chạy bên họ khi invoke method
  /// `onPressDoneAssigneePick`
  String get onDoneMethodName {
    if (arguments != null &&
        arguments!.isNotEmpty &&
        arguments!["on_done_method_name"] != null &&
        arguments!["on_done_method_name"] != "") {
      return arguments!["on_done_method_name"];
    }

    return "";
  }

  /*
    true: hiển thị button showSelectedMembers: khi click sẽ hiển thị toàn bộ members đã select trước đó (theo member ids)
    default value: true
  */
  bool get isShowBtnViewSelectedMembers {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["show_btn_view_selected_members"] != null) {
      return sConfigs!["show_btn_view_selected_members"];
    }

    return true;
  }

  /*
    true: search user trong workspace
    default value: true
  */
  bool get searchOnlyCurrentWorkspace {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["search_only_current_workspace"] != null) {
      return sConfigs!["search_only_current_workspace"];
    }
    return true;
  }

  /*
    true: sync selected member by ignore_removable_member
    default value: true
  */
  bool get needSyncIgnoreRemoveMembers {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["need_sync_ignore_remove_members"] != null) {
      return sConfigs!["need_sync_ignore_remove_members"];
    }
    return true;
  }

  /*
    true: sync selected member by ignore_removable_member
    default value: true
  */
  bool? get defaultCanPressDone {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["default_can_press_done"] != null) {
      return sConfigs!["default_can_press_done"];
    }
    return null;
  }

  /*
    true: run `checkHasAnySelectedItem`
    default value: true
  */
  bool? get needToCheckPressDone {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["need_to_check_press_done"] != null) {
      return sConfigs!["need_to_check_press_done"];
    }
    return null;
  }
}

/*
  V1.0.1: 
  - Thêm tree_id
  - bump department api version to 3.0
  - remove unused variables
  - Thêm config ẩn/hiện button back, done.
*/
mixin V1_0_1 {
  /*
    dynamic arguments sử dụng chung cho các trường hợp
    Nếu thêm case, nên ghi rõ các user-case tương ứng

    1. native pass:
    'ws_id' : '$ws_id'
    - Hiển thị departments theo ws_id hiện tại
  */
  Map<String, dynamic>? arguments;

  String get treeId {
    if (arguments != null &&
        arguments!.isNotEmpty &&
        arguments!["tree_id"] != null &&
        arguments!["tree_id"] != "") {
      return arguments!["tree_id"];
    }

    return "";
  }

  set treeId(String? id) {
    arguments ??= {};
    arguments?["tree_id"] = id;
  }

  // Thêm mode pick 1 hoặc pick nhiều, hiện tại apply cho toàn bộ tabs
  SelectInviteesPickMode selectInviteesPickMode =
      SelectInviteesPickMode.pickMultiple;

  /*
    dynamic configs sử dụng cho các trường hợp đặc biệt như ẩn button back, ẩn button done....

    1. native pass:
    'show_btn_back': true/false:
      - ẩn/hiện button back
      - defaultValue: true

    'show_btn_done': true/false
      - ẩn hiện button done
      - defaultValue: `pickMode` == `SelectInviteesPickMode.pickMultiple`
  */

  Map<String, dynamic>? sConfigs;

  bool isShowBtnBack() {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["show_btn_back"] != null) {
      return sConfigs!["show_btn_back"];
    }

    return true;
  }

  bool isShowBtnDone() {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["show_btn_done"] != null) {
      return sConfigs!["show_btn_done"];
    }

    return !selectInviteesPickMode.isPickOne;
  }

  /*
    allowPickAllDepartment:
    - true -> hiển thị `pickAllDepartmentTitle` thay cho departmentName
    Khi pick department, trả về id = workspace_id, thay vì department_id
  */
  Map? get _pickAllDepartmentMaps => arguments?["pick_all_department"];

  bool get allowPickAllDepartment {
    return _pickAllDepartmentMaps != null &&
        _pickAllDepartmentMaps!.isNotEmpty &&
        _pickAllDepartmentMaps?["allow"] == true;
  }

  String get pickAllDepartmentTitle {
    if (allowPickAllDepartment &&
        _pickAllDepartmentMaps?["title"] != null &&
        _pickAllDepartmentMaps?["title"] != "") {
      return _pickAllDepartmentMaps!["title"];
    }

    return LocaleKeys.calendar_select_invitees_pick_all_department_title.tr;
  }
}

mixin _V1_0_6 {
  // Các configs của phần member
  Map<String, dynamic>? memberConfigs;

  Map<String, dynamic>? addedMyAddminIds;

  List<int>? membersAddedByAdminIds;
  List<String>? threadAddedByAdminIds;
  List<String>? departmentAddedByAdminIds;
  List<String>? roleAddedByAdminIds;

  String isAddedByAdminStr() {
    if (memberConfigs != null &&
        memberConfigs!.isNotEmpty &&
        memberConfigs!["added_by_admin_str"] != null) {
      return memberConfigs!["added_by_admin_str"];
    }

    return LocaleKeys.calendar_select_invitees_is_added_by_addmin_str.tr;
  }

  /// Nếu truyền vào `inputMembers`,
  /// sẽ hiển thị danh sách `inputMembers`
  /// thay vì gọi api để get list members
  /// Không thể search members
  /// available on 1.1.1
  ListAPIResponse<Assignee>? inputMembers() {
    if (memberConfigs != null &&
        memberConfigs!.isNotEmpty &&
        memberConfigs!["input_members"] != null) {
      return memberConfigs!["input_members"];
    }

    return null;
  }

  bool get hasInputMembers {
    final input = inputMembers();

    return input != null;
  }

  void initAddedByAdminData() {
    if (addedMyAddminIds == null) {
      return;
    }

    final List<String> listMemberIds = addedMyAddminIds!['member'] != null
        ? List.from(
            addedMyAddminIds!['member'].where((e) => e != null && e != ""))
        : [];
    membersAddedByAdminIds =
        List<int>.from(listMemberIds.map((e) => int.parse(e)));

    threadAddedByAdminIds = addedMyAddminIds!['thread'] == null
        ? null
        : List<String>.from(addedMyAddminIds!['thread']);

    departmentAddedByAdminIds = addedMyAddminIds!['department'] == null
        ? null
        : List<String>.from(addedMyAddminIds!['department']);

    roleAddedByAdminIds = addedMyAddminIds!['role'] == null
        ? null
        : List<String>.from(addedMyAddminIds!['role']);
  }
}

mixin _ResultV1_0_6 {
  final Map<String, dynamic> _addedByAdmin = {};

  final List<Assignee> membersAddedByAdmin = [];
  final List<Conversation> threadsAddedByAdmin = [];
  final List<OrganizationDepartment> departmentsAddedByAdmin = [];
  final List<OrganizationRole> rolesAddedByAdmin = [];

  void setAddedByAdminListData({
    required List<Assignee> membersAddedByAdmin,
    required List<Conversation> threadsAddedByAdmin,
    required List<OrganizationDepartment> departmentsAddedByAdmin,
    required List<OrganizationRole> rolesAddedByAdmin,
  }) {
    this.membersAddedByAdmin.addAll(membersAddedByAdmin);
    this.threadsAddedByAdmin.addAll(threadsAddedByAdmin);
    this.departmentsAddedByAdmin.addAll(departmentsAddedByAdmin);
    this.rolesAddedByAdmin.addAll(rolesAddedByAdmin);

    _addedByAdmin.addAll(_toAddedByAdminJson());
  }

  Map<String, dynamic> _addedByAdminToJson() => _addedByAdmin;

  Map<String, dynamic> _toAddedByAdminJson() => {
        "added_by_admin": {
          "members":
              List<dynamic>.from(membersAddedByAdmin.map((e) => e.toJson())),
          "threads":
              List<dynamic>.from(threadsAddedByAdmin.map((e) => e.toJson())),
          "departments": List<dynamic>.from(
              departmentsAddedByAdmin.map((e) => e.toJson())),
          "roles": List<dynamic>.from(rolesAddedByAdmin.map((e) => e.toJson())),
        }
      };
}

extension ExtV107 on V1_0_1 {
  bool isReturnNotRemovableList() {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["return_removable_list"] != null) {
      return sConfigs!["return_removable_list"];
    }

    return false;
  }
}

extension ExtV106 on V1_0_1 {
  String emptyText() {
    if (sConfigs != null &&
        sConfigs!.isNotEmpty &&
        sConfigs!["empty_text"] != null) {
      final String s = sConfigs!["empty_text"];
      return s.isEmpty ? LocaleKeys.error_nodata.tr : s;
    }

    return LocaleKeys.error_nodata.tr;
  }
}

mixin _V1_0_7 {
  // ToanNM: lười không muốn tạo class
  Map<String, dynamic>? notRemovable;

  List<int>? notRemovableMemberIds;
  List<String>? notRemovableThreadIds;
  List<String>? notRemovableDepartmentIds;
  List<String>? notRemovableRoleIds;
  List<String>? notRemovableBotIds;

  void initNotRemovableData() {
    if (notRemovable == null) {
      return;
    }

    final List<String> listMemberIds = notRemovable!['member_ids'] != null
        ? List.from(
            notRemovable!['member_ids'].where((e) => e != null && e != ""))
        : [];
    notRemovableMemberIds =
        List<int>.from(listMemberIds.map((e) => int.parse(e)));

    notRemovableThreadIds = notRemovable!['thread_ids'] == null
        ? null
        : List<String>.from(notRemovable!['thread_ids']);

    notRemovableDepartmentIds = notRemovable!['department_ids'] == null
        ? null
        : List<String>.from(notRemovable!['department_ids']);

    notRemovableRoleIds = notRemovable!['role_ids'] == null
        ? null
        : List<String>.from(notRemovable!['role_ids']);

    notRemovableBotIds = notRemovable!['bot_ids'] == null
        ? null
        : List<String>.from(notRemovable!['bot_ids']);
  }
}

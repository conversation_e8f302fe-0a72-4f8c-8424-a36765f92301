# Changelog: Thê<PERSON> tính năng giới hạn số lượng item cho từng SelectInviteeTabs

## Tóm tắt thay đổi

Đã thêm tính năng cho phép thiết lập giới hạn số lượng item có thể chọn riêng biệt cho từng tab trong SelectInviteeTabs. Khi đạt giới hạn, hệ thống sẽ hiển thị bottomsheet cảnh báo.

## Files đã thay đổi

### 1. `app/core/gp_core/lib/models/orgchat/select_invitees_model.dart`

**Thêm mới:**
- Thu<PERSON>c tính `tabSelectionLimits: Map<String, int>?` trong `SelectInviteesOptions`
- Extension `TabSelectionLimit` với method `getMaxSelectionLimit()`
- Cập nhật constructor và `from<PERSON>son()` để hỗ trợ `tabSelectionLimits`

**Chi tiết:**
```dart
// Thêm thuộc tính mới
final Map<String, int>? tabSelectionLimits;

// Thêm extension
extension TabSelectionLimit on SelectInviteeTabs {
  int? getMaxSelectionLimit(SelectInviteesOptions options) {
    // Logic lấy giới hạn cho từng tab
  }
}
```

### 2. `app/features/member_picker/lib/invitees_list/invitees_list_controller.dart`

**Thêm mới:**
- Method `reachTabSelectionLimit` để kiểm tra giới hạn tab
- Cập nhật `onAssigneeUpdated()` để kiểm tra giới hạn tab member

**Chi tiết:**
```dart
bool get reachTabSelectionLimit {
  final tabLimit = SelectInviteeTabs.member.getMaxSelectionLimit(svo!);
  if (tabLimit != null && selectedItems.value.length >= tabLimit) {
    return true;
  }
  return false;
}
```

### 3. `app/features/member_picker/lib/department/organization_department_select_list_controller.dart`

**Thêm mới:**
- Import `LimitDialog`
- Cập nhật `onSelect()` để kiểm tra giới hạn tab department

**Chi tiết:**
```dart
void onSelect(OrganizationDepartment data, bool p0) {
  if (p0) {
    final tabLimit = SelectInviteeTabs.department.getMaxSelectionLimit(svo);
    if (tabLimit != null && selectedDepartment.value.length >= tabLimit) {
      // Hiển thị bottomsheet cảnh báo
      return;
    }
  }
  // Logic chọn bình thường
}
```

### 4. `app/features/member_picker/lib/roles/organization_role_select_list_controller.dart`

**Thêm mới:**
- Import `LimitDialog`
- Cập nhật `onSelectionChanged()` để kiểm tra giới hạn tab role

### 5. `app/features/member_picker/lib/group/group_select_list_controller.dart`

**Thêm mới:**
- Import `LimitDialog`
- Cập nhật `onConversationUpdated()` để kiểm tra giới hạn tab group

### 6. `app/features/member_picker/lib/bot/bot_controller.dart`

**Thêm mới:**
- Import `LimitDialog` và các dependencies
- Implement `ArgumentConfigMixin` và `ArgumentInitialMixin`
- Override `onSelected()` để kiểm tra giới hạn tab bot
- Thêm method `initArguments()`

### 7. `app/features/member_picker/lib/workspace/workspace_controller.dart`

**Thêm mới:**
- Import `LimitDialog` và `gp_core`
- Override `onSelected()` để kiểm tra giới hạn tab workspace

### 8. `app/features/member_picker/lib/selected_list/select_invitees_controller.dart`

**Thêm mới:**
- Gọi `initArguments()` cho `botController` và `workspaceController`

## Tính năng mới

### 1. Cấu hình giới hạn

```dart
final options = SelectInviteesOptions(
  tabSelectionLimits: {
    'member': 5,
    'department': 3,
    'role': 2,
    'group': 8,
    'bot': 1,
    'workspace': 1,
  },
);
```

### 2. Bottomsheet cảnh báo

Khi đạt giới hạn:
- Title: "Đã đạt giới hạn chọn [Tên Tab]"
- Content: "Bạn chỉ có thể chọn tối đa [số lượng] [tên tab]"
- Button: "Hiểu rồi"

### 3. Kiểm tra giới hạn

- Mỗi tab có logic kiểm tra riêng
- Chỉ kiểm tra khi user đang chọn thêm (không ảnh hưởng việc bỏ chọn)
- Hiển thị cảnh báo và ngăn không cho chọn thêm khi đạt giới hạn

## Tương thích ngược

- ✅ Code cũ không truyền `tabSelectionLimits` vẫn hoạt động bình thường
- ✅ Giới hạn `limitUsers` cũ vẫn hoạt động song song
- ✅ Không ảnh hưởng đến các tính năng hiện có
- ✅ Các tab không có giới hạn vẫn hoạt động như cũ

## Testing

Đã tạo test cases để kiểm tra:
- Lấy giới hạn đúng cho từng tab
- Xử lý trường hợp không có giới hạn
- Serialize/deserialize JSON
- Các edge cases

## Files hỗ trợ đã tạo

1. `example_tab_selection_limits.dart` - Ví dụ sử dụng
2. `test_tab_selection_limits.dart` - Test cases
3. `README_tab_selection_limits.md` - Hướng dẫn sử dụng
4. `CHANGELOG_tab_selection_limits.md` - File này

## Lưu ý khi sử dụng

1. Key trong `tabSelectionLimits` phải khớp với tên tab: 'member', 'department', 'role', 'group', 'bot', 'workspace'
2. Giá trị phải là số nguyên dương
3. Nếu không thiết lập giới hạn cho tab nào đó, tab đó sẽ không có giới hạn
4. Giới hạn áp dụng riêng biệt cho từng tab

# Tính năng giới hạn số lượng item cho từng SelectInviteeTabs

## Tổng quan

Tính năng này cho phép thiết lập giới hạn số lượng item có thể chọn riêng biệt cho từng tab trong SelectInviteeTabs. Khi user đạt đến giới hạn, hệ thống sẽ hiển thị bottomsheet cảnh báo và không cho phép chọn thêm.

## Cách sử dụng

### 1. Thiết lập giới hạn

```dart
final options = SelectInviteesOptions(
  title: "Chọn người tham gia",
  tabs: [
    SelectInviteeTabs.member,
    SelectInviteeTabs.department,
    SelectInviteeTabs.role,
  ],
  tabSelectionLimits: {
    'member': 5,        // Tối đa 5 member
    'department': 3,    // Tối đa 3 department
    'role': 2,          // Tối đa 2 role
  },
);
```

### 2. <PERSON><PERSON><PERSON> key hỗ trợ

- `'member'`: <PERSON><PERSON><PERSON><PERSON> hạn cho tab Member
- `'department'`: <PERSON><PERSON><PERSON><PERSON> hạn cho tab Department  
- `'role'`: Giới hạn cho tab Role
- `'group'`: Giới hạn cho tab Group
- `'bot'`: Giới hạn cho tab Bot
- `'workspace'`: Giới hạn cho tab Workspace

### 3. Trường hợp không giới hạn

```dart
// Cách 1: Không truyền tabSelectionLimits
final options1 = SelectInviteesOptions(
  title: "Không giới hạn",
  tabs: [SelectInviteeTabs.member],
);

// Cách 2: Truyền null
final options2 = SelectInviteesOptions(
  title: "Không giới hạn", 
  tabSelectionLimits: null,
);

// Cách 3: Chỉ giới hạn một số tab
final options3 = SelectInviteesOptions(
  title: "Giới hạn một phần",
  tabSelectionLimits: {
    'member': 10,  // Chỉ member có giới hạn
    // department, role, group, bot, workspace không giới hạn
  },
);
```

## Cách hoạt động

### 1. Kiểm tra giới hạn

Khi user chọn một item trong bất kỳ tab nào:

1. Hệ thống kiểm tra xem tab đó có giới hạn không
2. Nếu có giới hạn, kiểm tra số lượng đã chọn
3. Nếu đã đạt giới hạn: hiển thị bottomsheet cảnh báo
4. Nếu chưa đạt giới hạn: cho phép chọn bình thường

### 2. Bottomsheet cảnh báo

Khi đạt giới hạn, bottomsheet sẽ hiển thị:

- **Title**: "Đã đạt giới hạn chọn [Tên Tab]"
- **Content**: "Bạn chỉ có thể chọn tối đa [số lượng] [tên tab]"
- **Button**: "Hiểu rồi" để đóng bottomsheet

### 3. Tương thích ngược

- Code cũ không truyền `tabSelectionLimits` vẫn hoạt động bình thường
- Giới hạn `limitUsers` cũ vẫn hoạt động song song với giới hạn tab mới
- Không ảnh hưởng đến các tính năng hiện có

## Implementation Details

### Files được thay đổi:

1. **select_invitees_model.dart**: Thêm thuộc tính `tabSelectionLimits` và extension `TabSelectionLimit`
2. **invitees_list_controller.dart**: Thêm kiểm tra giới hạn cho tab member
3. **organization_department_select_list_controller.dart**: Thêm kiểm tra giới hạn cho tab department
4. **organization_role_select_list_controller.dart**: Thêm kiểm tra giới hạn cho tab role
5. **group_select_list_controller.dart**: Thêm kiểm tra giới hạn cho tab group
6. **bot_controller.dart**: Thêm kiểm tra giới hạn cho tab bot
7. **workspace_controller.dart**: Thêm kiểm tra giới hạn cho tab workspace
8. **select_invitees_controller.dart**: Khởi tạo `svo` cho bot và workspace controller

### Extension method:

```dart
extension TabSelectionLimit on SelectInviteeTabs {
  int? getMaxSelectionLimit(SelectInviteesOptions options) {
    // Trả về giới hạn cho tab hiện tại hoặc null nếu không có giới hạn
  }
}
```

## Testing

Chạy test để đảm bảo tính năng hoạt động đúng:

```bash
flutter test test_tab_selection_limits.dart
```

## Lưu ý

1. Giới hạn áp dụng riêng biệt cho từng tab
2. Nếu không thiết lập giới hạn cho tab nào đó, tab đó sẽ không có giới hạn
3. Giới hạn chỉ áp dụng khi user đang chọn thêm item, không ảnh hưởng đến việc bỏ chọn
4. Tính năng tương thích ngược hoàn toàn với code hiện có
